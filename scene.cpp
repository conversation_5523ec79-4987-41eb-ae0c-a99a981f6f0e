/*
 * Scene.cpp - Flappy Bird游戏场景类实现文件
 *
 * 实现说明：
 * 1. 游戏状态机的完整实现
 * 2. Qt Graphics View框架的深度应用
 * 3. 信号槽机制的综合运用
 * 4. 游戏对象生命周期的精确管理
 */

#include "scene.h"
#include <QKeyEvent>        // 键盘事件类
#include <birditem.h>       // 小鸟类
#include "ground.h"         // 地面类

/**
 * @brief Scene构造函数 - 游戏场景的完整初始化
 * @param parent 父对象指针
 *
 * 初始化列表说明：
 * - QGraphicsScene(parent): 调用父类构造函数
 * - startsign(0): 游戏开始标志初始化为false（开始界面状态）
 * - gameoverbool(0): 游戏结束标志初始化为false（正常状态）
 * - score(0): 分数初始化为0
 *
 * 构造函数执行流程：
 * 1. 设置管道生成定时器
 * 2. 创建开始界面UI元素
 * 3. 创建游戏结束界面UI元素（预创建但不显示）
 * 4. 初始化地面对象
 */
Scene::Scene(QObject *parent) : QGraphicsScene(parent),startsign(0),gameoverbool(0),score(0)
{
    // === 第一步：初始化定时器系统 ===
    setpipetimer();             // 设置管道生成定时器和相关信号连接

    // === 第二步：创建开始界面背景 ===
    startImage = new QGraphicsPixmapItem(QPixmap(":/new/prefix1/start.png"));
    addItem(startImage);        // 添加到场景中
    startImage->setPos(0,0);    // 设置位置为场景左上角
    startImage->setZValue(100); // 设置Z轴层级为100，确保在最前面显示
                                // Z值越大，显示层级越高

    // === 第三步：创建开始界面Logo ===
    QPixmap startLogoPixmap(":/new/prefix1/zangda_main.png");
    // 加载原始Logo图片

    QPixmap scaledStartLogo = startLogoPixmap.scaled(200, 150, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    // 图片缩放处理：
    // - 目标尺寸：200x150像素
    // - Qt::KeepAspectRatio：保持宽高比，防止图片变形
    // - Qt::SmoothTransformation：使用平滑变换算法，提高缩放质量

    startLogoImage = new QGraphicsPixmapItem(scaledStartLogo);
    addItem(startLogoImage);    // 添加到场景

    // 计算Logo居中位置
    qreal logoX = (432 - startLogoImage->boundingRect().width()) / 2;
    // 432是场景宽度，boundingRect().width()是Logo实际宽度
    // 居中公式：(容器宽度 - 对象宽度) / 2

    startLogoImage->setPos(logoX, 450);     // 设置居中位置，Y坐标450
    startLogoImage->setZValue(99);          // Z层级99，略低于背景但高于游戏对象

    // === 第四步：预创建游戏结束界面元素 ===
    // 注意：这些元素在构造时创建但不添加到场景，游戏结束时才显示

    gameoverImage = new QGraphicsPixmapItem(QPixmap(":/new/prefix1/gameover.png"));
    // 游戏结束背景图片

    QPixmap originalPixmap(":/new/prefix1/zangda.png");
    QPixmap scaledPixmap = originalPixmap.scaled(150, 100, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    nankaiImage = new QGraphicsPixmapItem(scaledPixmap);
    // 游戏结束Logo，同样进行缩放处理

    // === 第五步：初始化地面对象 ===
    ground = new groundItem;    // 创建地面对象
    addItem(ground);            // 添加到场景
    ground->setZValue(10);      // 设置Z层级为10，高于背景但低于小鸟和管道
                                // 层级关系：背景(0) < 地面(10) < 小鸟(20) < UI(100)
}

/**
 * @brief 小鸟对象定义和初始化
 *
 * 功能详解：
 * 1. 创建小鸟对象，使用第一帧动画图片作为初始显示
 * 2. 将小鸟添加到游戏场景中
 * 3. 设置小鸟的显示层级
 *
 * 设计考虑：
 * - 小鸟使用0.png作为初始图片，这是动画序列的第一帧
 * - Z层级设置为20，确保小鸟显示在地面(10)之上，UI元素(100)之下
 */
void Scene::birddef()
{
    bird = new birditem(QPixmap(":/new/prefix1/0.png"));
    // 创建小鸟对象，传入初始图片
    // birditem构造函数会设置小鸟的初始位置和属性

    addItem(bird);              // 将小鸟添加到场景的图形项列表中
    bird->setZValue(20);        // 设置Z轴层级，确保正确的显示顺序
                                // 层级关系：背景 < 地面(10) < 小鸟(20) < 管道 < UI(100)
}

/**
 * @brief 游戏主要开始流程
 *
 * 状态转换：开始界面 → 游戏进行状态
 *
 * 执行步骤：
 * 1. 设置游戏状态标志
 * 2. 清理开始界面UI元素
 * 3. 启动小鸟的物理动画
 * 4. 开始管道生成定时器
 *
 * 内存管理：
 * - 使用removeItem()从场景中移除对象
 * - 使用delete释放对象内存，防止内存泄漏
 */
void Scene::mainstart()
{
    // === 第一步：状态标志设置 ===
    startsign=1;                // 设置游戏开始标志为true
                                // 这个标志用于keyPressEvent中判断当前游戏状态

    // === 第二步：清理开始界面背景 ===
    removeItem(startImage);     // 从场景中移除开始界面背景
    delete startImage;          // 释放内存
                                // 注意：必须先removeItem再delete，否则可能导致崩溃

    // === 第三步：清理开始界面Logo ===
    removeItem(startLogoImage); // 从场景中移除Logo
    delete startLogoImage;      // 释放内存

    // === 第四步：启动小鸟动画 ===
    bird->birdstart();          // 调用小鸟的开始方法
                                // 这会启动小鸟的下落动画和翅膀扇动动画

    // === 第五步：启动管道生成系统 ===
    if(!pipetimer->isActive()){ // 检查定时器是否已经在运行
        pipetimer->start(2000); // 启动定时器，每2000毫秒(2秒)生成一个管道
                                // 这个间隔决定了游戏的难度
    }
}

/**
 * @brief 分数增加方法
 *
 * 调用时机：
 * - 当小鸟成功通过一对管道时
 * - 通常在PipeItem类中检测到小鸟通过时调用
 *
 * 功能：
 * - 简单的分数递增
 * - 分数会在游戏结束时通过showscore()方法显示
 */
void Scene::Scoreadd()
{
    score++;                    // 分数加1
                                // 这个方法通常由管道对象在检测到小鸟通过时调用
}

/**
 * @brief 设置管道生成定时器和碰撞检测系统
 *
 * 核心功能：
 * 1. 创建管道生成定时器
 * 2. 设置定时器超时处理逻辑
 * 3. 建立碰撞检测信号槽连接
 * 4. 实现游戏的核心循环机制
 *
 * 设计模式：
 * - 使用Lambda表达式简化信号槽连接
 * - 使用Qt的信号槽机制实现松耦合的碰撞检测
 * - 采用定时器驱动的游戏循环
 */
void Scene::setpipetimer()
{
    // === 第一步：创建定时器对象 ===
    pipetimer = new QTimer(this);   // 创建定时器，this作为父对象确保内存管理
                                    // QTimer是Qt的定时器类，用于周期性执行任务

    // === 第二步：连接定时器信号到管道生成逻辑 ===
    connect(pipetimer,&QTimer::timeout,[=](){
        // 使用Lambda表达式作为槽函数，[=]表示按值捕获所有外部变量
        // timeout信号在定时器到期时发出

        // --- 创建新管道对象 ---
        PipeItem* pipe = new PipeItem;  // 创建管道对象
                                        // PipeItem构造函数会设置管道的位置、动画等

        // --- 设置管道碰撞检测 ---
        connect(pipe,&PipeItem::collidesignal,[=](){
            // 当管道检测到与小鸟碰撞时触发
            // collidesignal是PipeItem类中定义的自定义信号

            pipetimer->stop();          // 停止管道生成定时器
                                        // 防止游戏结束后继续生成管道
            gameover();                 // 调用游戏结束处理方法
        });

        // --- 设置小鸟碰撞检测 ---
        connect(bird,&birditem::collidesignal2,[=](){
            // 当小鸟检测到碰撞时触发
            // collidesignal2是birditem类中定义的自定义信号
            // 这通常是小鸟撞到地面或场景边界时触发

            gameover();                 // 直接调用游戏结束处理
                                        // 注意：这里不需要停止pipetimer，gameover()方法会处理
        });

        // --- 将管道添加到场景 ---
        addItem(pipe);                  // 将新创建的管道添加到场景中
                                        // 管道会自动开始移动动画
    });

    // 注意：定时器在这里只是创建和配置，实际启动在mainstart()方法中
    // 这种设计允许在游戏开始前完成所有初始化工作
}

/**
 * @brief 游戏结束处理方法
 *
 * 状态转换：游戏进行 → 游戏结束状态
 *
 * 处理流程：
 * 1. 设置游戏结束状态标志
 * 2. 停止所有游戏对象的动画
 * 3. 显示最终分数
 * 4. 显示游戏结束界面
 * 5. 停止管道生成系统
 *
 * 设计要点：
 * - 确保所有动画都被正确停止
 * - 正确设置UI元素的显示层级
 * - 使用类型转换安全地处理场景中的对象
 */
void Scene::gameover()
{
    // === 第一步：设置游戏状态 ===
    gameoverbool=1;             // 设置游戏结束标志为true
                                // 这个标志用于keyPressEvent中判断是否可以重启游戏

    // === 第二步：停止游戏对象动画 ===
    bird->birdstop();           // 停止小鸟的所有动画（下落、旋转、翅膀扇动）
    ground->groundstop();       // 停止地面的滚动动画

    // === 第三步：显示最终分数 ===
    showscore();                // 调用分数显示方法，在屏幕中央显示分数

    // === 第四步：显示游戏结束背景 ===
    addItem(gameoverImage);     // 添加游戏结束背景图片到场景
    gameoverImage->setPos(0,0); // 设置位置为场景左上角，覆盖整个游戏区域
    gameoverImage->setZValue(100); // 设置最高显示层级，确保在所有游戏对象之上

    // === 第五步：显示游戏结束Logo ===
    addItem(nankaiImage);       // 添加结束界面Logo到场景

    // 计算Logo居中位置
    qreal centerX = (432 - nankaiImage->boundingRect().width()) / 2;
    // 使用与开始界面相同的居中算法

    nankaiImage->setPos(centerX, 450);  // 设置居中位置
    nankaiImage->setZValue(100);        // 设置与背景相同的高层级

    // === 第六步：停止所有管道的动画 ===
    QList<QGraphicsItem*> sceneItems = items();
    // 获取场景中所有图形项的列表
    // items()返回场景中所有QGraphicsItem对象的列表

    for(int i=0; i<sceneItems.size(); i++){
        // 遍历所有场景对象

        PipeItem * pipe = qgraphicsitem_cast<PipeItem*>(sceneItems[i]);
        // 使用qgraphicsitem_cast进行安全的类型转换
        // 如果对象是PipeItem类型，返回转换后的指针；否则返回nullptr
        // 这是Qt Graphics View框架中推荐的类型检查方式

        if(pipe){
            pipe->pipestop();       // 如果是管道对象，停止其移动动画
        }
    }

    // === 第七步：停止管道生成系统 ===
    pipetimer->stop();          // 停止定时器，不再生成新的管道
                                // 这确保游戏结束后不会有新的障碍物出现
}

/**
 * @brief 分数显示方法
 *
 * 功能详解：
 * 1. 清理之前的分数显示对象（如果存在）
 * 2. 创建新的文本对象显示当前分数
 * 3. 设置字体样式和颜色
 * 4. 计算居中位置并添加到场景
 *
 * 设计考虑：
 * - 使用HTML格式支持富文本显示
 * - 选择等宽字体确保数字对齐
 * - 使用自定义颜色提高视觉效果
 * - 动态计算居中位置适应不同分数长度
 */
void Scene::showscore()
{
    // === 第一步：清理旧的分数显示对象 ===
    if(scoretext && scoretext->scene()) {
        // 检查分数文本对象是否存在且在场景中
        // scoretext != nullptr 确保对象存在
        // scoretext->scene() != nullptr 确保对象已添加到场景

        removeItem(scoretext);      // 从场景中移除
        delete scoretext;           // 释放内存
        scoretext = nullptr;        // 重置指针，防止悬空指针
    }

    // === 第二步：创建新的分数文本对象 ===
    scoretext = new QGraphicsTextItem();
    // QGraphicsTextItem是Qt Graphics View框架中用于显示文本的类

    QString lastscore="分数：" + QString::number(score);
    // 构建分数显示字符串
    // QString::number()将整数转换为字符串

    scoretext->setHtml(lastscore);  // 设置文本内容，支持HTML格式
                                    // 使用HTML而不是纯文本可以支持富文本格式

    // === 第三步：设置字体样式 ===
    QFont font("Consolas",20,QFont::Bold);
    // 创建字体对象：
    // - "Consolas"：等宽字体，确保数字对齐美观
    // - 20：字体大小（像素）
    // - QFont::Bold：粗体样式，提高可读性

    scoretext->setFont(font);       // 应用字体设置

    // === 第四步：设置文本颜色 ===
    QColor color(126,12,110);       // 创建自定义颜色（紫红色）
                                    // RGB值：R=126, G=12, B=110
    scoretext->setDefaultTextColor(color);  // 设置文本颜色

    // === 第五步：添加到场景并设置位置 ===
    addItem(scoretext);             // 将文本对象添加到场景

    // 计算水平居中位置
    qreal textCenterX = (432 - scoretext->boundingRect().width()) / 2;
    // boundingRect()返回文本的边界矩形
    // width()获取文本的实际宽度
    // 居中公式：(场景宽度 - 文本宽度) / 2

    scoretext->setPos(textCenterX, 280);    // 设置文本位置
                                            // Y坐标280使文本显示在屏幕中央偏上位置
}

/**
 * @brief 键盘事件处理方法
 * @param event 键盘事件对象
 *
 * 游戏状态机实现：
 * 1. 开始界面状态：空格键启动游戏
 * 2. 游戏进行状态：空格键控制小鸟跳跃
 * 3. 游戏结束状态：空格键重启游戏
 *
 * 设计模式：
 * - 状态模式：根据游戏状态执行不同操作
 * - 命令模式：将键盘输入转换为游戏命令
 *
 * 重写说明：
 * - 重写QGraphicsScene::keyPressEvent()方法
 * - 处理完自定义逻辑后调用父类方法确保事件传播
 */
void Scene::keyPressEvent(QKeyEvent *event)
{
    // === 检查按键类型 ===
    if(event->key() == Qt::Key_Space) {
        // 只响应空格键，这是Flappy Bird的经典操作方式

        // === 游戏状态判断和相应处理 ===
        if(gameoverbool) {
            // 状态：游戏结束
            // 操作：重启游戏
            restart();              // 调用重启方法，重置所有游戏状态
        }
        else if(startsign == 0) {
            // 状态：开始界面（游戏未开始）
            // 操作：启动游戏
            mainstart();            // 调用游戏开始方法，进入游戏状态
        }
        else {
            // 状态：游戏进行中
            // 操作：小鸟跳跃
            bird->jump();           // 调用小鸟跳跃方法，产生向上的推力
        }
    }

    // === 调用父类方法确保事件正确传播 ===
    QGraphicsScene::keyPressEvent(event);
    // 这确保其他可能需要处理键盘事件的对象也能收到事件
    // 这是Qt事件处理的最佳实践
}

/**
 * @brief 游戏重启方法
 *
 * 状态转换：游戏结束 → 开始界面状态
 *
 * 重启流程：
 * 1. 清理游戏结束界面UI元素
 * 2. 清理所有游戏对象（管道、分数显示等）
 * 3. 重置游戏状态标志和分数
 * 4. 重新创建开始界面UI元素
 * 5. 重新初始化游戏对象
 * 6. 恢复地面动画
 * 7. 确保定时器系统正确重置
 *
 * 设计要点：
 * - 完整的内存管理，防止内存泄漏
 * - 正确的对象生命周期管理
 * - 状态重置的完整性
 * - UI元素的正确重建
 */
void Scene::restart()
{
    // === 第一步：清理游戏结束界面UI元素 ===

    // 清理游戏结束背景图片
    if(gameoverImage && gameoverImage->scene()) {
        // 双重检查：对象存在且在场景中
        removeItem(gameoverImage);      // 从场景移除
        delete gameoverImage;           // 释放内存
        gameoverImage = nullptr;        // 重置指针
    }

    // 清理游戏结束Logo图片
    if(nankaiImage && nankaiImage->scene()) {
        removeItem(nankaiImage);
        delete nankaiImage;
        nankaiImage = nullptr;
    }

    // 清理分数显示文本
    if(scoretext && scoretext->scene()) {
        removeItem(scoretext);
        delete scoretext;
        scoretext = nullptr;
    }

    // === 第二步：清理所有管道对象 ===
    QList<QGraphicsItem*> sceneItems = items();
    // 获取场景中所有图形项

    for(int i = sceneItems.size()-1; i >= 0; i--) {
        // 从后向前遍历，避免在遍历过程中修改列表导致的问题
        // 这是删除容器元素时的安全做法

        PipeItem* pipe = qgraphicsitem_cast<PipeItem*>(sceneItems[i]);
        // 安全类型转换，检查是否为管道对象

        if(pipe) {
            removeItem(pipe);           // 从场景移除管道
            delete pipe;                // 释放管道对象内存
        }
    }

    // === 第三步：重置游戏状态 ===
    gameoverbool = false;               // 重置游戏结束标志
    startsign = false;                  // 重置游戏开始标志，回到开始界面状态
    score = 0;                          // 重置分数为0
    scoretext = nullptr;                // 确保分数文本指针为空

    // === 第四步：重新创建游戏结束界面元素（预创建但不显示） ===
    // 这些对象需要重新创建，因为之前已经被删除
    gameoverImage = new QGraphicsPixmapItem(QPixmap(":/new/prefix1/gameover.png"));

    QPixmap originalPixmap(":/new/prefix1/zangda.png");
    QPixmap scaledPixmap = originalPixmap.scaled(150, 100, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    nankaiImage = new QGraphicsPixmapItem(scaledPixmap);

    // === 第五步：重新创建开始界面UI元素 ===

    // 创建开始界面背景
    startImage = new QGraphicsPixmapItem(QPixmap(":/new/prefix1/start.png"));
    addItem(startImage);                // 立即添加到场景显示
    startImage->setPos(0,0);
    startImage->setZValue(100);

    // 创建开始界面Logo
    QPixmap startLogoPixmap(":/new/prefix1/zangda_main.png");
    QPixmap scaledStartLogo = startLogoPixmap.scaled(200, 150, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    startLogoImage = new QGraphicsPixmapItem(scaledStartLogo);
    addItem(startLogoImage);            // 立即添加到场景显示

    // 计算Logo居中位置
    qreal logoX = (432 - startLogoImage->boundingRect().width()) / 2;
    startLogoImage->setPos(logoX, 450);
    startLogoImage->setZValue(99);

    // === 第六步：重新初始化小鸟对象 ===
    if(bird && bird->scene()) {
        // 如果小鸟对象存在，先清理
        removeItem(bird);
        delete bird;
    }
    birddef();                          // 重新创建小鸟对象
                                        // 这会将小鸟重置到初始位置和状态

    // === 第七步：恢复地面动画 ===
    ground->groundani->start();         // 重新启动地面滚动动画
                                        // 地面对象本身不需要重新创建，只需重启动画

    // === 第八步：确保定时器系统正确重置 ===
    if(pipetimer->isActive()) {
        pipetimer->stop();              // 如果定时器还在运行，停止它
                                        // 定时器会在下次游戏开始时重新启动
    }

    // 重启完成，游戏回到开始界面状态
    // 用户按空格键即可重新开始游戏
}
