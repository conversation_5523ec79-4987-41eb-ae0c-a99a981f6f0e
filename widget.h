/*
 * Widget.h - Flappy Bird游戏主窗口类头文件
 *
 * 功能概述：
 * 1. 继承自QWidget，作为游戏的主窗口容器
 * 2. 管理UI界面和游戏场景的集成
 * 3. 负责窗口的基本设置（标题、图标、大小等）
 *
 * 设计模式：
 * - 使用Qt的Model-View架构，Widget作为View层
 * - Scene作为游戏逻辑的核心控制器
 * - UI文件分离界面设计和逻辑代码
 */

#ifndef WIDGET_H
#define WIDGET_H

#include <QWidget>      // Qt基础窗口类
#include "scene.h"      // 游戏场景类

QT_BEGIN_NAMESPACE
namespace Ui { class Widget; }  // UI命名空间声明，用于访问.ui文件生成的界面
QT_END_NAMESPACE

/**
 * @class Widget
 * @brief Flappy Bird游戏主窗口类
 *
 * 职责：
 * 1. 窗口管理：设置窗口标题、图标、大小
 * 2. 界面集成：将QGraphicsView与Scene连接
 * 3. 资源管理：管理UI对象的生命周期
 * 4. 事件分发：将窗口事件传递给游戏场景
 */
class Widget : public QWidget
{
    Q_OBJECT    // Qt元对象系统宏，支持信号槽机制

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针，用于Qt的父子对象管理
     *
     * 主要工作：
     * 1. 初始化UI界面
     * 2. 创建游戏场景
     * 3. 设置窗口属性
     * 4. 建立View-Scene连接
     */
    Widget(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     *
     * 负责清理UI资源，Scene由Qt的父子对象系统自动管理
     */
    ~Widget();

private:
    Ui::Widget *ui;     // UI界面指针，由Qt Designer生成的界面类
    Scene* scene;       // 游戏场景指针，核心游戏逻辑控制器
};

#endif // WIDGET_H
