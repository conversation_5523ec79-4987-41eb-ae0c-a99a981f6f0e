#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计Qt项目代码行数的脚本
"""

import os
import glob
from pathlib import Path

def count_lines_in_file(file_path):
    """统计单个文件的行数"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            total_lines = len(lines)
            # 统计非空行
            non_empty_lines = len([line for line in lines if line.strip()])
            # 统计注释行（简单判断以//或/*开头的行）
            comment_lines = len([line for line in lines if line.strip().startswith('//') or line.strip().startswith('/*') or line.strip().startswith('*')])
            return total_lines, non_empty_lines, comment_lines
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return 0, 0, 0

def main():
    """主函数"""
    # 定义要统计的文件类型
    source_extensions = ['.cpp', '.h', '.c', '.hpp']
    other_extensions = ['.pro', '.ui', '.qrc']
    
    print("=" * 60)
    print("Qt Flappy Bird 项目代码行数统计")
    print("=" * 60)
    
    # 统计源代码文件
    total_source_lines = 0
    total_source_non_empty = 0
    total_source_comments = 0
    source_files = []
    
    # 统计其他项目文件
    total_other_lines = 0
    other_files = []
    
    # 遍历当前目录下的所有文件
    for file_path in Path('.').rglob('*'):
        if file_path.is_file():
            file_ext = file_path.suffix.lower()
            
            # 跳过一些不需要统计的文件
            if file_path.name in ['flappy.pro.user'] or file_path.suffix in ['.png', '.ico', '.jpg', '.jpeg']:
                continue
                
            if file_ext in source_extensions:
                lines, non_empty, comments = count_lines_in_file(file_path)
                source_files.append((str(file_path), lines, non_empty, comments))
                total_source_lines += lines
                total_source_non_empty += non_empty
                total_source_comments += comments
                
            elif file_ext in other_extensions:
                lines, _, _ = count_lines_in_file(file_path)
                other_files.append((str(file_path), lines))
                total_other_lines += lines
    
    # 输出源代码文件统计
    print("\n📁 源代码文件统计:")
    print("-" * 60)
    print(f"{'文件名':<25} {'总行数':<8} {'非空行':<8} {'注释行':<8}")
    print("-" * 60)
    
    for file_path, lines, non_empty, comments in sorted(source_files):
        filename = os.path.basename(file_path)
        print(f"{filename:<25} {lines:<8} {non_empty:<8} {comments:<8}")
    
    print("-" * 60)
    print(f"{'源代码文件总计':<25} {total_source_lines:<8} {total_source_non_empty:<8} {total_source_comments:<8}")
    
    # 输出其他项目文件统计
    if other_files:
        print("\n📄 其他项目文件统计:")
        print("-" * 40)
        print(f"{'文件名':<25} {'行数':<8}")
        print("-" * 40)
        
        for file_path, lines in sorted(other_files):
            filename = os.path.basename(file_path)
            print(f"{filename:<25} {lines:<8}")
        
        print("-" * 40)
        print(f"{'其他文件总计':<25} {total_other_lines:<8}")
    
    # 总体统计
    print("\n📊 项目总体统计:")
    print("=" * 40)
    print(f"源代码文件数量: {len(source_files)}")
    print(f"源代码总行数: {total_source_lines}")
    print(f"源代码非空行数: {total_source_non_empty}")
    print(f"源代码注释行数: {total_source_comments}")
    print(f"有效代码行数: {total_source_non_empty - total_source_comments}")
    print(f"其他项目文件行数: {total_other_lines}")
    print(f"项目文件总行数: {total_source_lines + total_other_lines}")
    print("=" * 40)
    
    # 统计图片资源
    pic_files = list(Path('pics').glob('*')) if Path('pics').exists() else []
    if pic_files:
        print(f"\n🖼️  图片资源文件: {len(pic_files)} 个")
        for pic in sorted(pic_files):
            if pic.is_file():
                size = pic.stat().st_size
                print(f"  - {pic.name} ({size} bytes)")

if __name__ == "__main__":
    main()
