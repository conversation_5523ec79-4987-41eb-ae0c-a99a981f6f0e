/*
 * Widget.cpp - Flappy Bird游戏主窗口类实现文件
 *
 * 实现细节：
 * 1. 窗口初始化和配置
 * 2. 游戏场景的创建和设置
 * 3. 背景图片的加载和定位
 * 4. Graphics View框架的集成
 */

#include "widget.h"
#include "ui_widget.h"          // UI类定义，由Qt Designer自动生成
#include <QGraphicsPixmapItem>  // 图形项类，用于显示图片
#include <QIcon>                // 窗口图标类

/**
 * @brief Widget构造函数 - 游戏窗口的完整初始化过程
 * @param parent 父窗口指针
 *
 * 初始化流程详解：
 * 1. 调用父类构造函数，建立Qt对象层次结构
 * 2. 创建UI对象并设置到当前窗口
 * 3. 配置窗口属性（标题、图标）
 * 4. 创建游戏场景并设置尺寸
 * 5. 加载背景图片并添加到场景
 * 6. 初始化小鸟对象
 * 7. 将场景绑定到图形视图组件
 */
Widget::Widget(QWidget *parent)
    : QWidget(parent)           // 调用父类构造函数，建立Qt对象树
    , ui(new Ui::Widget)        // 创建UI对象，包含界面布局和控件
{
    // === 第一步：UI界面初始化 ===
    ui->setupUi(this);          // 将UI文件中定义的界面应用到当前窗口
                                // 这会创建所有控件并设置布局

    // === 第二步：窗口属性设置 ===
    setWindowTitle("FlappyBird");                                    // 设置窗口标题
    setWindowIcon(QIcon(":/new/prefix1/logo.ico"));                // 设置窗口图标
                                                                    // 使用Qt资源系统加载图标

    // === 第三步：创建游戏场景 ===
    scene = new Scene(this);    // 创建游戏场景，包含所有游戏内容

    // === 第四步：设置场景大小 ===
    scene->setSceneRect(0,0,432,644);  // 设置游戏场景的大小为432x644像素

    // === 第五步：添加背景图片 ===
    QGraphicsPixmapItem* pixItem = new QGraphicsPixmapItem(QPixmap(":/new/prefix1/bg.png"));
    // 创建背景图片对象

    scene->addItem(pixItem);    // 把背景图片放到游戏场景里
    pixItem->setPos(0,0);       // 背景图片放在左上角

    // === 第六步：创建小鸟 ===
    scene->birddef();           // 在场景中创建小鸟

    // === 第七步：显示游戏 ===
    ui->Box->setScene(scene);   // 把游戏场景显示到界面上
}

/**
 * @brief Widget析构函数 - 资源清理
 *
 * 清理说明：
 * 1. 只需要删除ui指针，因为它不在Qt对象树中
 * 2. scene对象由于设置了parent为this，会被Qt自动清理
 * 3. Qt的父子对象系统确保所有子对象都会被正确释放
 */
Widget::~Widget()
{
    delete ui;  // 释放UI对象内存
                // scene等其他对象由Qt的父子对象系统自动管理
}