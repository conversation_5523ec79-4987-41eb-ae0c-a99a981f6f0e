<?xml version="1.0" encoding="UTF-8"?>
<!--
    Widget.ui - Flappy Bird游戏主窗口界面设计文件

    文件说明：
    1. 这是Qt Designer生成的UI描述文件，使用XML格式
    2. 定义了游戏主窗口的布局和控件属性
    3. 在编译时会生成对应的C++头文件ui_widget.h
    4. 采用Qt的Model-View架构，这里定义View层

    设计理念：
    - 简洁的界面设计，只包含一个QGraphicsView控件
    - 固定窗口尺寸，适配游戏内容
    - 使用Graphics View框架支持复杂的2D游戏场景
-->

<ui version="4.0">
 <!-- UI文件版本，4.0是Qt5/Qt6的标准版本 -->

 <class>Widget</class>
 <!-- 定义UI类名，将生成Ui::Widget类 -->

 <!--
    主窗口Widget定义
    - 继承自QWidget，作为游戏的顶级窗口
    - 包含游戏的所有UI元素
 -->
 <widget class="QWidget" name="Widget">

  <!-- 窗口几何属性：位置和尺寸 -->
  <property name="geometry">
   <rect>
    <x>0</x>        <!-- 窗口X坐标：0（相对于父窗口） -->
    <y>0</y>        <!-- 窗口Y坐标：0（相对于父窗口） -->
    <width>434</width>   <!-- 窗口宽度：434像素，适配游戏场景宽度432+边框 -->
    <height>646</height> <!-- 窗口高度：646像素，适配游戏场景高度644+边框 -->
   </rect>
  </property>

  <!-- 窗口最小尺寸限制 -->
  <property name="minimumSize">
   <size>
    <width>0</width>     <!-- 最小宽度：0表示无限制 -->
    <height>0</height>   <!-- 最小高度：0表示无限制 -->
   </size>
  </property>

  <!-- 窗口最大尺寸限制 -->
  <property name="maximumSize">
   <size>
    <width>16777215</width>  <!-- 最大宽度：Qt的最大值，实际上无限制 -->
    <height>16777215</height> <!-- 最大高度：Qt的最大值，实际上无限制 -->
   </size>
  </property>

  <!-- 窗口标题（会被代码中的setWindowTitle覆盖） -->
  <property name="windowTitle">
   <string>Widget</string>
  </property>

  <!--
    QGraphicsView控件 - 游戏显示的核心组件

    功能说明：
    1. Qt Graphics View框架的视图组件
    2. 用于显示QGraphicsScene中的所有游戏对象
    3. 支持2D图形渲染、动画、交互等功能
    4. 提供视图变换（缩放、旋转、平移）能力

    设计考虑：
    - 尺寸与主窗口完全一致，实现全屏游戏体验
    - 名称为"Box"，在代码中通过ui->Box访问
    - 位置(0,0)确保填满整个主窗口
  -->
  <widget class="QGraphicsView" name="Box">

   <!-- Graphics View的几何属性 -->
   <property name="geometry">
    <rect>
     <x>0</x>        <!-- X坐标：0，与主窗口左边对齐 -->
     <y>0</y>        <!-- Y坐标：0，与主窗口顶部对齐 -->
     <width>434</width>   <!-- 宽度：与主窗口相同 -->
     <height>646</height> <!-- 高度：与主窗口相同 -->
    </rect>
   </property>

   <!-- Graphics View的最小尺寸 -->
   <property name="minimumSize">
    <size>
     <width>0</width>     <!-- 最小宽度：无限制 -->
     <height>0</height>   <!-- 最小高度：无限制 -->
    </size>
   </property>

   <!-- Graphics View的最大尺寸 -->
   <property name="maximumSize">
    <size>
     <width>16777215</width>  <!-- 最大宽度：无限制 -->
     <height>16777215</height> <!-- 最大高度：无限制 -->
    </size>
   </property>

   <!--
    注意：QGraphicsView的其他重要属性在代码中设置：
    1. setScene() - 绑定游戏场景
    2. 渲染选项 - 抗锯齿、缓存等
    3. 视图策略 - 更新策略、拖拽模式等
    4. 背景画刷 - 背景颜色或图片
   -->
  </widget>
 </widget>

 <!--
    资源部分 - 当前为空
    如果需要在UI文件中直接引用资源，可以在这里定义
    但本项目的资源通过resources.qrc文件管理
 -->
 <resources/>

 <!--
    信号槽连接部分 - 当前为空
    可以在Qt Designer中直接建立控件间的信号槽连接
    但本项目的所有连接都在代码中动态建立，提供更大的灵活性
 -->
 <connections/>
</ui>

<!--
    UI文件的编译过程：
    1. Qt的UIC工具读取此文件
    2. 生成ui_widget.h头文件
    3. 包含Ui::Widget类的定义
    4. setupUi()方法用于初始化界面

    在代码中的使用：
    1. #include "ui_widget.h"
    2. ui = new Ui::Widget
    3. ui->setupUi(this)
    4. 通过ui->Box访问QGraphicsView控件
-->
