/*
 * Scene.h - Flappy Bird游戏场景类头文件
 *
 * 核心职责：
 * 1. 游戏状态管理：开始界面、游戏进行、游戏结束
 * 2. 游戏对象管理：小鸟、管道、地面、UI元素
 * 3. 游戏逻辑控制：碰撞检测、计分系统、定时器管理
 * 4. 用户交互处理：键盘事件响应
 *
 * 设计模式：
 * - 状态模式：通过布尔标志管理游戏状态
 * - 观察者模式：使用Qt信号槽机制处理对象间通信
 * - 组合模式：管理多个游戏对象的生命周期
 */

#ifndef SCENE_H
#define SCENE_H

// === Qt框架头文件 ===
#include <QTimer>               // 定时器类，用于管道生成和游戏循环
#include <QGraphicsScene>       // 图形场景基类，Qt Graphics View框架核心
#include <QGraphicsPixmapItem>  // 图形项类，用于显示静态图片
#include <QGraphicsTextItem>    // 文本图形项，用于显示分数

// === 游戏对象头文件 ===
#include <birditem.h>           // 小鸟类，游戏主角
#include "pipeitem.h"           // 管道类，游戏障碍物
#include "ground.h"             // 地面类，游戏边界

/**
 * @class Scene
 * @brief Flappy Bird游戏场景控制器
 *
 * 继承自QGraphicsScene，是Qt Graphics View框架的核心组件
 *
 * 主要功能模块：
 * 1. 游戏状态管理：开始、进行、结束、重启
 * 2. 对象生命周期管理：创建、更新、销毁游戏对象
 * 3. 事件处理：键盘输入、定时器事件、碰撞事件
 * 4. UI管理：界面切换、分数显示、图片展示
 */
class Scene : public QGraphicsScene
{
    Q_OBJECT    // Qt元对象系统，支持信号槽和属性系统

public:
    /**
     * @brief 构造函数 - 初始化游戏场景
     * @param parent 父对象指针
     *
     * 初始化内容：
     * 1. 设置管道生成定时器
     * 2. 创建开始界面UI元素
     * 3. 创建游戏结束界面UI元素
     * 4. 初始化地面对象
     * 5. 设置游戏状态标志
     */
    explicit Scene(QObject *parent = nullptr);

    // === 公共接口方法 ===

    /**
     * @brief 小鸟对象定义和初始化
     *
     * 功能：
     * 1. 创建小鸟对象
     * 2. 设置小鸟初始位置和层级
     * 3. 添加到场景中
     */
    void birddef();

    /**
     * @brief 游戏主要开始流程
     *
     * 功能：
     * 1. 移除开始界面元素
     * 2. 启动小鸟动画
     * 3. 开始管道生成定时器
     * 4. 设置游戏进行状态
     */
    void mainstart();

    /**
     * @brief 分数增加方法
     *
     * 当小鸟成功通过管道时调用
     */
    void Scoreadd();

    /**
     * @brief 游戏重启方法
     *
     * 功能：
     * 1. 清理当前游戏状态
     * 2. 重置所有游戏对象
     * 3. 恢复到开始界面
     * 4. 重新初始化游戏参数
     */
    void restart();

private:
    // === 私有方法 ===

    /**
     * @brief 设置管道生成定时器
     *
     * 功能：
     * 1. 创建定时器对象
     * 2. 连接定时器信号到管道生成槽
     * 3. 设置碰撞检测信号连接
     */
    void setpipetimer();

    /**
     * @brief 游戏结束处理
     *
     * 功能：
     * 1. 停止所有动画和定时器
     * 2. 显示游戏结束界面
     * 3. 显示最终分数
     * 4. 设置游戏结束状态
     */
    void gameover();

    /**
     * @brief 显示分数
     *
     * 功能：
     * 1. 创建分数文本对象
     * 2. 设置字体和颜色
     * 3. 计算居中位置
     * 4. 添加到场景中
     */
    void showscore();

    // === 游戏对象成员变量 ===
    QTimer* pipetimer;              // 管道生成定时器，控制障碍物出现频率
    birditem* bird;                 // 小鸟对象指针，游戏主角
    groundItem* ground;             // 地面对象指针，游戏边界

    // === UI界面元素 ===
    QGraphicsPixmapItem* startImage;       // 开始界面背景图片
    QGraphicsPixmapItem* startLogoImage;   // 开始界面Logo图片
    QGraphicsPixmapItem* gameoverImage;    // 游戏结束界面背景图片
    QGraphicsPixmapItem* nankaiImage;      // 游戏结束界面Logo图片
    QGraphicsTextItem* scoretext;          // 分数显示文本对象

    // === 游戏状态标志 ===
    bool startsign;                 // 游戏开始标志：false=开始界面，true=游戏进行
    bool gameoverbool;              // 游戏结束标志：false=正常状态，true=游戏结束
    int score;                      // 当前游戏分数

protected:
    /**
     * @brief 键盘事件处理函数
     * @param event 键盘事件对象
     *
     * 处理逻辑：
     * 1. 检测空格键按下
     * 2. 根据游戏状态执行不同操作：
     *    - 开始界面：启动游戏
     *    - 游戏进行：小鸟跳跃
     *    - 游戏结束：重启游戏
     */
    void keyPressEvent(QKeyEvent *event);
};

#endif // SCENE_H
